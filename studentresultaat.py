# -*- coding: utf-8 -*-
"""

@author: BIM Docent
"""
import pandas as pd
import numpy as np
import sys, sqlalchemy, requests, csv, random
from datetime import timedelta, datetime

#---FUNCTIES------------------------------------------------
def ConnectDB():
#Deze functie zorgt voor de verbinding van python met de lokale mamp database 
#(local host: 127.0.0.1) op de computer. Indien er een goede verbinding kan
#worden gemaakt wordt de boodschap success teruggegeven en anders een 
#foutmelding. Let op pymysql moet wel zijn geinstalleerd in de python omgeving
#die wordt gebruikt, In ons geval anaconda!
    try:
        db_connector = 'mysql+pymysql://'
        db_username = 'root:'
        db_password = 'root@'
        db_ip       = '127.0.0.1/'
        db_name     = 'studentresultaat'
        engine_name = \
            db_connector + \
            db_username + \
            db_password + \
            db_ip       + \
            db_name
        engine = sqlalchemy.create_engine(engine_name,  execution_options={"isolation_level": "AUTOCOMMIT"})
        #engine = OpenDB()
        con = engine.connect()
        return con, 'success'
    except Exception as e:
        return '', e

def DfToDb(con, df, table):
#Deze functie zorgt voor het inserten van een pandas dataframe in een MySQL DB
#als input wordt gegeven: 
#de database verbinding
#het dataframe
#de Db tabelnaam    
    try:
        df.to_sql(table, con, if_exists = 'append', index = False, chunksize = 1000)
        print('Insert in', table, 'database tabel succesvol')
    except Exception as e:
        print('Insert in', table, 'niet succesvol', e)
        print(type(e))
        sys.exit()

def QueryDB(con, table):
#Deze functie zorgt voor het uitvoeren van een SQL statemetn dat het aantal
#records in een tabel telt. Als input wordt de database verbiding en de tabel
#naam gegeven
    sql = 'SELECT COUNT(*) FROM '+table
    result = con.execute(sql).fetchone()
    print('Aantal records in meubelsdim.'+table+':', result['COUNT(*)'])

def uitkomst(cijfer):
#Deze functie heeft als input een cijfer en bepallt of het een O (onvoldoende)
#of V (voldoende is)
    if cijfer <5.4:
        return 'O'
    else:
        return 'V'
#---EINDE FUNCTIES------------------------------------------


#---HOOFDPROGRAMMA------------------------------------------

#-----EXTRACT---------------------------------------

#---Inlezen CSV naar dataframe
df = pd.read_csv('studentresultaat-etl.csv', delimiter=";", encoding="utf-8-sig", decimal = ',')


#---TRANSFORM---------------------------------------

#-----Betrouwbaarheids controle en opschonen
#-------Er zijn rijen in het bestand waarbij geen cijfer is ingevuld. Dit zijn
#-------onbetrouwbare regels en die mogen eruit. Eerst gaan we tellen hoeveel
#-------regels er in het dataframe zitten zonder cijfer. 
#-------zoek mbv de website https://datatofish.com/check-nan-pandas-dataframe/
#-------uit met welke python code het aantal regels zonder cijfer kan tellen en
#-------print dat aantal. In python pandas wordt een leeg veld met NaN aangegeven

# -----><zet hier de python code><-------


#-------Verwijder nu de regels in de dataframe zonder een cijfer
#-------We hebben al eerder gezien dat met dropna lege velden verwijderd
#-------kunnen worden. Maar dropna zonder kolom naam verwijdert alle regels
#-------met een leeg veld, dus will we dropna toepassen met een kolomnaam
#-------Open https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.dropna.html
#-------en kijk hoe je met subset kolomnamen kunt opgeven bij dropna, denk ook
#-------aan inplace = True (waarom?). Print voor en na het verwijderen van 
#-------de regels de lengte van de dataframe

# -----><zet hier de python code><-------


#-----Student dataframe afsplitsen en opschonen
#------Copieer de juiste velden voor de studentdataframe van het origineel
#------noem dit nieuwe datafram dfS

# -----><zet hier de python code><-------

#Omdat dezelfde student in het oorspronkelijke dataframe meerdere keren voorkomt
#moeten dubbele rijen eruit omdat we in de studenten dimensie tabel de student
#maar 1x willen hebben

# -----><zet hier de python code><-------

# print een status regel:
print('De student-dimensie dataframe bevat', len(dfS), 'rijen')
#-----Student dataframe klaar voor LOAD

#-----Docent dataframe afsplitsen en opschonen
#Maak nu  de docent dataframe aan met 
#de juiste velden en verwijder ook eventuele dubbele records. 
#Print ook het aantal rijen in de dataframe
#Kijk voor de naam van dit dataframe onder in dit programma bij:
#VUL DB TABELLEN MET DATAFRAMES

# -----><zet hier de python code><-------

#-----Docent dataframe klaar voor LOAD

#-----Cursus dataframe afsplitsen, opschonen en aanvullen
#Maak nu de cursus dataframe aan met 
#de juiste velden, verwijder ook eventuele dubbele records
#Voeg een extra kolom toe met de temperatuur in fahrenheit, zoek de formule
#om celsius om te zetten in fahrenheit op internet 
#Print ook het aantal rijen in de dataframe

# -----><zet hier de python code><-------

#-----Cursus dataframe klaar voor LOAD


#-----Klas dataframe afsplitsen en opschonen
#Maak de klas dataframe aan
#Print ook het aantal rijen in de dataframe

# -----><zet hier de python code><-------

#-----Klas dataframe klaar voor LOAD


#-----Opleiding dataframe afsplitsen en opschonen
# Maak de opleiding dataframe aan
#Print ook het aantal rijen in de dataframe

# -----><zet hier de python code><-------

#-----Opleiding dataframe klaar voor LOAD


#-----Datum dataframe aanmaken
start='2015-01-01'
end='2025-12-31'
dfDt = pd.DataFrame({"datum": pd.date_range(start, end)})
dfDt["jaar"] = dfDt['datum'].dt.year
dfDt["maand"] = dfDt['datum'].dt.month
dfDt["dag"] = dfDt['datum'].dt.day
dfDt["week"] = dfDt['datum'].dt.isocalendar().week
dfDt["jaardag"] = dfDt['datum'].dt.day_of_year
dfDt["kwartaal"] = dfDt['datum'].dt.quarter
dfDt["jaarhelft"] = (dfDt['kwartaal'] + 1) // 2
dfDt["weekdagnummer"] = dfDt['datum'].dt.weekday+1
days = {1:'Maandag',2:'Dinsdag',3:'Woensdag',4:'Donderdag',5:'Vrijdag',6:'Zaterdag',7:'Zondag'}
dfDt['weekdagnaam'] = dfDt['weekdagnummer'].apply(lambda x: days[x])
weekend = {1: True, 0: False}
dfDt['weekend'] = dfDt['weekdagnummer'].apply(lambda x: weekend[x//6])
print('De datum-dimensie dataframe bevat', len(dfDt), 'rijen')
#-----Datum dataframe klaar voor LOAD

#-----Resultaat dataframe afsplitsen opschonen en aanvullen
#Maak hier de resultaat feiten dataframe aan 
#met de juiste velden. Gebruik de functie uitkomst om afhankelijk van het cijfer
#een V of O in het juiste veld te krijgen. Deze code krijg je cadeau
#Print ook het aantal rijen in de dataframe

# -----><zet hier de python code><-------

#Bepaal O of V met de functie uitkomst die door apply en lambda per per rij
#wordt aangeroepen. De returnwaarde van de functie wordt in de datafame gezet
dfR['uitkomst'] = dfR['tentamencijfer'].apply(lambda x: uitkomst(x))

#-----Resultaat dataframe klaar voor LOAD

#---EINDE TRANSFORM-------------------------------


#---lOAD---------------------------------------
#---Database connect---------
# LET op, hier worden de namen van de dataframes gebruikt die hierboven zijn 
# aangemaakt. Die namen moeten overeenkomen! Ook moeten de namen van de tabellen
# overeenkomen met de namen die in jouw database zitten en moeten de namen van
# de velden overeenkomen met de kolomnamen in de dataframes 
 

con, e = ConnectDB()
if e == 'success':
    # VERWIJDER DATA IN DB TABELLEN--------------
    #Eerst worden alle tabellen in de DB geleegd
    con.execute("SET FOREIGN_KEY_CHECKS = 0")
    con.execute("TRUNCATE TABLE resultaatfeiten")
    con.execute("TRUNCATE TABLE studentdimensie")
    con.execute("TRUNCATE TABLE docentdimensie")
    con.execute("TRUNCATE TABLE cursusdimensie")
    con.execute("TRUNCATE TABLE klasdimensie")
    con.execute("TRUNCATE TABLE opleidingdimensie")
    con.execute("TRUNCATE TABLE datumdimensie")
    con.execute("SET FOREIGN_KEY_CHECKS = 1")
        
    # VUL DB TABELLEN MET DATAFRAMES
    print("-------------------")    
    DfToDb(con, dfS, 'studentdimensie')
    DfToDb(con, dfD, 'docentdimensie')
    DfToDb(con, dfC, 'cursusdimensie')
    DfToDb(con, dfK, 'klasdimensie')
    DfToDb(con, dfO, 'opleidingdimensie')
    DfToDb(con, dfDt, 'datumdimensie')
    DfToDb(con, dfR, 'resultaatfeiten')
else:
    print('database error', e)
    
print("-------------------")
#Tellen hoeveel records er in de DB tabellen zijn gezet
QueryDB(con, 'studentdimensie')
QueryDB(con, 'docentdimensie')
QueryDB(con, 'cursusdimensie')
QueryDB(con, 'klasdimensie')
QueryDB(con, 'opleidingdimensie')
QueryDB(con, 'datumdimensie')
QueryDB(con, 'resultaatfeiten')

#---EINDE lOAD---------------------------------

#---EINDE HOOFDPORGRAMMA------------------------------------
