<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="id-actor-medewerker"/>
    <element xsi:type="archimate:BusinessActor" name="Klantenservice" id="id-actor-klantenservice"/>
    <element xsi:type="archimate:BusinessActor" name="Vervoerder" id="id-actor-vervoerder"/>
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    <element xsi:type="archimate:BusinessService" name="Opslag en Distributie" id="id-service-opslag-distributie"/>
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-process-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Order handmatig invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens controleren" id="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad valideren" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-process-magazijnbeheer"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking en Packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel handmatig maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking handmatig kopieren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:BusinessProcess" name="Status opzoeken" id="id-process-status-opzoeken"/>
    <element xsi:type="archimate:BusinessRole" name="Magazijnmedewerker" id="id-role-magazijnmedewerker"/>
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    <element xsi:type="archimate:BusinessRole" name="Klantenservice medewerker" id="id-role-klantenservice-medewerker"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
    <element xsi:type="archimate:ApplicationService" name="API-koppeling" id="id-service-api"/>
    <element xsi:type="archimate:ApplicationService" name="E-mail interface" id="id-service-email"/>
    <element xsi:type="archimate:ApplicationService" name="Batchbestand verwerking" id="id-service-batch"/>
    <element xsi:type="archimate:ApplicationService" name="Klantgegevens beheer" id="id-service-klantbeheer"/>
    <element xsi:type="archimate:ApplicationService" name="Voorraad beheer" id="id-service-voorraad"/>
    <element xsi:type="archimate:ApplicationService" name="Picklijst generatie" id="id-service-picklijst"/>
    <element xsi:type="archimate:ApplicationService" name="Verzendlabel service" id="id-service-verzendlabel"/>
    <element xsi:type="archimate:ApplicationService" name="Tracking service" id="id-service-tracking"/>
    <element xsi:type="archimate:ApplicationService" name="Klantcontact beheer" id="id-service-klantcontact"/>
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-data-order"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-data-klant"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-data-voorraad"/>
    <element xsi:type="archimate:DataObject" name="Verzenddata" id="id-data-verzend"/>
    <element xsi:type="archimate:DataObject" name="Trackingdata" id="id-data-tracking"/>
    <element xsi:type="archimate:DataObject" name="Klantcontactdata" id="id-data-klantcontact"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:Node" name="TMS Server" id="id-node-standalone-tms"/>
    <element xsi:type="archimate:Node" name="CRM Server" id="id-node-standalone-crm"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk" id="id-network"/>
    <element xsi:type="archimate:Device" name="Werkstation medewerker" id="id-device-werkstation"/>
    <element xsi:type="archimate:Device" name="Magazijn terminal" id="id-device-magazijn"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-medewerker-orderverwerker" source="id-actor-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-medewerker-magazijn" source="id-actor-medewerker" target="id-role-magazijnmedewerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-klantenservice-role" source="id-actor-klantenservice" target="id-role-klantenservice-medewerker"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-ontvangen" source="id-process-orderverwerking" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-invoeren" source="id-process-orderverwerking" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-klantcheck" source="id-process-orderverwerking" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-voorraad" source="id-process-magazijnbeheer" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-picking" source="id-process-magazijnbeheer" target="id-process-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-verzendlabel" source="id-process-magazijnbeheer" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-tracking" source="id-process-magazijnbeheer" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-ontvangen-invoeren" source="id-process-order-ontvangen" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-invoeren-klantcheck" source="id-process-order-invoeren" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-klantcheck-voorraad" source="id-process-klantgegevens-check" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-voorraad-picking" source="id-process-voorraad-check" target="id-process-picking-packing"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-picking-verzendlabel" source="id-process-picking-packing" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-verzendlabel-tracking" source="id-process-verzendlabel-maken" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-tracking-klantcommunicatie" source="id-process-tracking-kopieren" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-orderverwerking" source="id-role-orderverwerker" target="id-process-orderverwerking"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-magazijnmedewerker-magazijnbeheer" source="id-role-magazijnmedewerker" target="id-process-magazijnbeheer"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantenservice-communicatie" source="id-role-klantenservice-medewerker" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantenservice-status" source="id-role-klantenservice-medewerker" target="id-process-status-opzoeken"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-api" source="id-app-erp" target="id-service-api"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-email" source="id-app-erp" target="id-service-email"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-batch" source="id-app-erp" target="id-service-batch"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-klantbeheer" source="id-app-erp" target="id-service-klantbeheer"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-wms-voorraad" source="id-app-wms" target="id-service-voorraad"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-wms-picklijst" source="id-app-wms" target="id-service-picklijst"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-service-verzendlabel"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-service-tracking"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-crm-klantcontact" source="id-app-crm" target="id-service-klantcontact"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-api-order-ontvangen" source="id-service-api" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-email-order-ontvangen" source="id-service-email" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-batch-order-ontvangen" source="id-service-batch" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantbeheer-klantcheck" source="id-service-klantbeheer" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-voorraad-voorraadcheck" source="id-service-voorraad" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-picklijst-picking" source="id-service-picklijst" target="id-process-picking-packing"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-verzendlabel-verzendlabel" source="id-service-verzendlabel" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-tracking-tracking" source="id-service-tracking" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantcontact-communicatie" source="id-service-klantcontact" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantcontact-status" source="id-service-klantcontact" target="id-process-status-opzoeken"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-orderdata" source="id-app-erp" target="id-data-order"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-klantdata" source="id-app-erp" target="id-data-klant"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-wms-voorraaddata" source="id-app-wms" target="id-data-voorraad"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-verzenddata" source="id-app-tms" target="id-data-verzend"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-trackingdata" source="id-app-tms" target="id-data-tracking"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-crm-klantcontactdata" source="id-app-crm" target="id-data-klantcontact"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-orderverwerking-efulfilment" source="id-process-orderverwerking" target="id-service-efulfilment"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-magazijnbeheer-opslag" source="id-process-magazijnbeheer" target="id-service-opslag-distributie"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-server-tms" source="id-node-standalone-tms" target="id-app-tms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-crm-server-crm" source="id-node-standalone-crm" target="id-app-crm"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-cloud" source="id-network" target="id-node-cloud"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-onpremise" source="id-network" target="id-node-onpremise"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-tms" source="id-network" target="id-node-standalone-tms"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-crm" source="id-network" target="id-node-standalone-crm"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-werkstation" source="id-network" target="id-device-werkstation"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-magazijn" source="id-network" target="id-device-magazijn"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-werkstation-orderverwerker" source="id-device-werkstation" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-werkstation-klantenservice" source="id-device-werkstation" target="id-role-klantenservice-medewerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-magazijn-terminal-magazijn" source="id-device-magazijn" target="id-role-magazijnmedewerker"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-vervoerder-verzendlabel" source="id-actor-vervoerder" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-vervoerder-tracking" source="id-actor-vervoerder" target="id-process-tracking-kopieren"/>
  </folder>
</archimate:model>
