import os
from PIL import Image, ImageDraw, ImageFont
import textwrap

def create_poster():
    # Poster dimensions (A1 size in pixels at 300 DPI)
    width = 2480
    height = 3508
    
    # Create image
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # Colors
    capgemini_blue = '#0070AD'
    cyber_orange = '#FF6B35'
    dark_gray = '#333333'
    light_gray = '#f8f9fa'
    
    # Convert hex to RGB
    def hex_to_rgb(hex_color):
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    blue_rgb = hex_to_rgb(capgemini_blue)
    orange_rgb = hex_to_rgb(cyber_orange)
    gray_rgb = hex_to_rgb(dark_gray)
    light_gray_rgb = hex_to_rgb(light_gray)
    
    # Try to use system fonts, fallback to default
    try:
        title_font = ImageFont.truetype("arial.ttf", 120)
        subtitle_font = ImageFont.truetype("arial.ttf", 60)
        section_font = ImageFont.truetype("arialbd.ttf", 80)
        text_font = ImageFont.truetype("arial.ttf", 50)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        section_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
    
    # Header background
    draw.rectangle([0, 0, width, 500], fill=blue_rgb)
    
    # Main title
    title_text = "DROOMSTAGE CYBERSECURITY"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((width - title_width) // 2, 80), title_text, fill='white', font=title_font)
    
    # Subtitle
    subtitle_text = "BIJ CAPGEMINI"
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    draw.text(((width - subtitle_width) // 2, 220), subtitle_text, fill='white', font=subtitle_font)
    
    # Company info
    company_text = "Global Technology Consulting • 200.000+ Medewerkers • Fortune 500 Klanten"
    company_bbox = draw.textbbox((0, 0), company_text, font=text_font)
    company_width = company_bbox[2] - company_bbox[0]
    draw.text(((width - company_width) // 2, 320), company_text, fill='white', font=text_font)
    
    # Content sections
    sections = [
        {
            'title': '🎯 ACTIVITEITEN & WERKZAAMHEDEN',
            'items': [
                '• Penetration Testing & Ethical Hacking',
                '• Security Assessments & Risk Analysis',
                '• Incident Response & Threat Intelligence',
                '• Compliance Audits (ISO 27001, GDPR)',
                '• Security Consulting & Strategy Development',
                '• Vulnerability Analysis & Management',
                '• Security Awareness Training',
                '• Crisis Management & Recovery'
            ],
            'x': 100, 'y': 600
        },
        {
            'title': '👥 WERKOMGEVING & COLLEGA\'S',
            'items': [
                '• Diverse internationale teams',
                '• Mentorship & continuous learning',
                '• Innovatieve cultuur & tech talks',
                '• Multidisciplinaire samenwerking',
                '• Knowledge sharing sessies',
                '• Toegang tot top certificeringen',
                '• Professional development tracks',
                '• Global project opportunities'
            ],
            'x': 1290, 'y': 600
        },
        {
            'title': '📍 WERKOMSTANDIGHEDEN',
            'items': [
                '• Op locatie: Utrecht/Amsterdam',
                '• Moderne kantoren & security labs',
                '• Klantbezoeken & internationale projecten',
                '• 24/7 Security Operations Center',
                '• State-of-the-art tools & technologie',
                '• Goede bereikbaarheid OV/auto',
                '• Work-life balance focus',
                '• Hybride werkmogelijkheden'
            ],
            'x': 100, 'y': 1800
        },
        {
            'title': '🏢 TYPE BEDRIJF - CAPGEMINI',
            'items': [
                '• Global Technology Consulting Leader',
                '• 200.000+ medewerkers wereldwijd',
                '• Fortune 500 klanten portfolio',
                '• Gespecialiseerde Cybersecurity divisie',
                '• Innovation labs & research centers',
                '• Strategic partnerships (Microsoft, AWS, Google)',
                '• Actief in 50+ landen, 40+ jaar ervaring',
                '• Focus op digital transformation & security'
            ],
            'x': 1290, 'y': 1800,
            'highlight': True
        }
    ]
    
    # Draw sections
    for section in sections:
        # Section background
        section_height = 1000
        section_width = 1100
        
        if section.get('highlight'):
            draw.rectangle([section['x']-20, section['y']-20, 
                          section['x']+section_width+20, section['y']+section_height+20], 
                         fill=light_gray_rgb)
            title_color = orange_rgb
        else:
            draw.rectangle([section['x']-20, section['y']-20, 
                          section['x']+section_width+20, section['y']+section_height+20], 
                         fill='white')
            title_color = blue_rgb
        
        # Section title
        draw.text((section['x'], section['y']), section['title'], fill=title_color, font=section_font)
        
        # Section items
        y_offset = section['y'] + 120
        for item in section['items']:
            # Wrap text if too long
            wrapped_lines = textwrap.wrap(item, width=45)
            for line in wrapped_lines:
                draw.text((section['x'], y_offset), line, fill=gray_rgb, font=text_font)
                y_offset += 70
    
    # Footer
    footer_y = height - 400
    draw.rectangle([0, footer_y, width, height], fill=orange_rgb)
    
    # Footer text
    footer_text = "🔒 KLAAR OM DE DIGITALE WERELD VEILIGER TE MAKEN! 🛡️"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=title_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    draw.text(((width - footer_width) // 2, footer_y + 100), footer_text, fill='white', font=title_font)
    
    return img

if __name__ == "__main__":
    try:
        poster = create_poster()
        poster.save("Cybersecurity_Droomstage_Poster.jpg", "JPEG", quality=95, dpi=(300, 300))
        print("✅ Poster succesvol aangemaakt: Cybersecurity_Droomstage_Poster.jpg")
        print("📏 Formaat: A1 (2480x3508 pixels, 300 DPI)")
        print("🎨 Klaar voor upload naar Canva!")
    except Exception as e:
        print(f"❌ Fout bij maken poster: {e}")
        print("💡 Installeer PIL/Pillow: pip install Pillow")
