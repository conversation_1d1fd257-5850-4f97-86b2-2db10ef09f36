-- -----------------------------------------------------
-- Schema studentresultaat dimensionaal model
-- Studentversie
-- -----------------------------------------------------
DROP DATABASE IF EXISTS `studentresultaat`;
CREATE SCHEMA IF NOT EXISTS `studentresultaat` DEFAULT CHARACTER SET utf8 ;
USE `studentresultaat` ;

-- -----------------------------------------------------
-- dimensie tabel student
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`studentdimensie`;
#Zet hier het sql CREATE commando met de juiste velden, velddefinitie en primary key die nodig is om de cursus dimensie tabel te definiëren
#Let op de veldnamen moeten zelfde zijn zijn als de namen in de header van het csv bestand
CREATE TABLE IF NOT EXISTS `studentresultaat`.`studentdimensie` (
  `studentnummer` INT(10) NOT NULL,
  `studentnaam` VARCHAR(50),
  `studentleeftijd` INT(2),
  PRIMARY KEY (`studentnummer`) )
DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- dimensie tabel docent
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`docentdimensie`;
CREATE TABLE IF NOT EXISTS `studentresultaat`.`docentdimensie` (
   `docentnummer` INT(10) NOT NULL,
   `docentnaam` CHAR(50),
  PRIMARY KEY (`docentnummer`) )
DEFAULT CHARACTER SET = utf8;

-- -----------------------------------------------------
-- dimensie tabel cursus
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`cursusdimensie`;

#Zet hier het sql CREATE commando met de juiste velden, velddefinitie en primary key die nodig is om de cursus dimensie tabel te definiëren
#Let op de veldnamen moeten zelfde zijn zijn als de namen in de header van het csv bestand

DEFAULT CHARACTER SET = utf8;

-- -----------------------------------------------------
-- dimensie tabel klas
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`klasdimensie`;

#Zet hier het sql CREATE commando met de juiste velden, velddefinitie en primary key die nodig is om de klas dimensie tabel te definiëren
#Let op de veldnamen moeten zelfde zijn zijn als de namen in de header van het csv bestand

DEFAULT CHARACTER SET = utf8;


-- -----------------------------------------------------
-- dimensie tabel opleiding
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`opleidingdimensie`;
CREATE TABLE IF NOT EXISTS `studentresultaat`.`opleidingdimensie` (
   `opleidingnummer` INT(3) NOT NULL,
   `opleidingnaam` CHAR(15),
   `opleidingmanager` VARCHAR(30),
  PRIMARY KEY (`opleidingnummer`) )
DEFAULT CHARACTER SET = utf8;

-- -----------------------------------------------------
-- dimensie tabel datum
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`datumdimensie`;
CREATE TABLE IF NOT EXISTS `studentresultaat`.`datumdimensie` (
   `datum` DATE NOT NULL,
   `jaar` INT(4),
   `maand` INT(2),
   `dag` INT(2),
   `week` INT(2),
   `jaardag` INT(3),
   `kwartaal` INT(1),
   `jaarhelft` INT(1),
   `weekdagnummer` INT(1),
   `weekdagnaam` VARCHAR(15),
   `weekend` TINYINT(1),
   PRIMARY KEY (`datum`) )
DEFAULT CHARACTER SET = utf8;

-- -----------------------------------------------------
-- feiten tabel resultaat
-- -----------------------------------------------------
DROP TABLE IF EXISTS `studentresultaat`.`resultaatfeiten`;
CREATE TABLE IF NOT EXISTS `studentresultaat`.`resultaatfeiten` (
    `resultaatnummer` INT(10) NOT NULL,
    `tentamencijfer` DECIMAL(3,1),
	`studentnummer` INT(10) NOT NULL,
	`docentnummer` INT(10) NOT NULL,
	`cursusnummer` CHAR(3) NOT NULL,
	`klasnummer` CHAR(6) NOT NULL,
	`opleidingnummer` INT(3) NOT NULL,
	`tentamendatum` DATE NOT NULL, 
    `uitkomst` CHAR(1),
   PRIMARY KEY (`resultaatnummer`) ,
   CONSTRAINT `student`
    FOREIGN KEY (`studentnummer`)
    REFERENCES `studentresultaat`.`studentdimensie` (`studentnummer`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
    
   CONSTRAINT `docent`
    FOREIGN KEY (`docentnummer`)
    REFERENCES `studentresultaat`.`docentdimensie` (`docentnummer`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
   CONSTRAINT `cursus`
    FOREIGN KEY (`cursusnummer`)
    REFERENCES `studentresultaat`.`cursusdimensie` (`cursusnummer`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
   CONSTRAINT `klas`
    FOREIGN KEY (`klasnummer`)
    REFERENCES `studentresultaat`.`klasdimensie` (`klasnummer`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
   CONSTRAINT `opleiding`
    FOREIGN KEY (`opleidingnummer`)
    REFERENCES `studentresultaat`.`opleidingdimensie` (`opleidingnummer`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
     CONSTRAINT `datum`
     FOREIGN KEY (`tentamendatum`)
     REFERENCES `studentresultaat`.`datumdimensie` (`datum`)
     ON DELETE NO ACTION
    ON UPDATE NO ACTION )
DEFAULT CHARACTER SET = utf8;